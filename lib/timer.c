#include "timer.h"

/**
 * @brief 初始化定时器0
 * @param mode 工作模式
 * @param ms 定时时间(毫秒)
 */
void Timer0_Init(timer_mode_t mode, unsigned int ms)
{
#if TIMER0_ENABLE
    unsigned long counts;

    // 清除定时器0的控制位
    TMOD &= 0xF0;

    // 设置定时器0工作模式
    TMOD |= (unsigned char)mode;

    // 计算定时器初值
    if(mode == TIMER_MODE_0 || mode == TIMER_MODE_1) {
        // 模式0和1为16位定时器
        counts = (FOSC / 12000) * ms;
        if(counts > 65536) {
            counts = 65536;  // 最大计数值限制
        }
        counts = 65536 - counts;
        TH0 = (unsigned char)(counts >> 8);
        TL0 = (unsigned char)counts;
    } else if(mode == TIMER_MODE_2) {
        // 模式2为8位自动重装
        counts = (FOSC / 12000) * ms / 256;
        if(counts > 256) {
            counts = 256;  // 最大计数值限制
        }
        counts = 256 - counts;
        TH0 = TL0 = (unsigned char)counts;
    } else if(mode == TIMER_MODE_3) {
        // 模式3: 双8位定时器，只设置TL0
        counts = (FOSC / 12000) * ms / 256;
        if(counts > 256) {
            counts = 256;
        }
        counts = 256 - counts;
        TL0 = (unsigned char)counts;
    }

    ET0 = 1;  // 允许定时器0中断
    TF0 = 0;  // 清除溢出标志
#endif
}

/**
 * @brief 初始化定时器1
 * @param mode 工作模式
 * @param ms 定时时间(毫秒)
 */
void Timer1_Init(timer_mode_t mode, unsigned int ms)
{
#if TIMER1_ENABLE
    unsigned long counts;

    // 清除定时器1的控制位
    TMOD &= 0x0F;

    // 设置定时器1工作模式
    TMOD |= ((unsigned char)mode << 4);

    // 计算定时器初值
    if(mode == TIMER_MODE_0 || mode == TIMER_MODE_1) {
        // 模式0和1为16位定时器
        counts = (FOSC / 12000) * ms;
        if(counts > 65536) {
            counts = 65536;  // 最大计数值限制
        }
        counts = 65536 - counts;
        TH1 = (unsigned char)(counts >> 8);
        TL1 = (unsigned char)counts;
    } else if(mode == TIMER_MODE_2) {
        // 模式2为8位自动重装
        counts = (FOSC / 12000) * ms / 256;
        if(counts > 256) {
            counts = 256;  // 最大计数值限制
        }
        counts = 256 - counts;
        TH1 = TL1 = (unsigned char)counts;
    }

    ET1 = 1;  // 允许定时器1中断
    TF1 = 0;  // 清除溢出标志
#endif
}

