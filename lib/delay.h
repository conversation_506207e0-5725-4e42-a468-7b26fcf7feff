#ifndef __DELAY_H__
#define __DELAY_H__

#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include "config.h"

/**
 * @brief 使用定时器的毫秒延时函数
 * @param ms 延时的毫秒数
 */
void delay_ms_timer(unsigned int ms);

/**
 * @brief 使用循环的毫秒延时函数
 * @param ms 延时的毫秒数
 */
void delay_ms_loop(unsigned int ms);

/**
 * @brief 统一的延时函数接口，根据配置选择实现方式
 * @param ms 延时的毫秒数
 */
void delay_ms(unsigned int ms);

#endif
