#!/bin/bash

# STC89C52RC 模板项目构建脚本
# 支持编译所有demo为独立的hex文件

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 编译器设置
CC=sdcc
CFLAGS="-mmcs51 -I./inc -I./lib"

# 目录设置
SRC_DIR="src"
LIB_DIR="lib"
BUILD_DIR="build"
OBJ_DIR="$BUILD_DIR/obj"
HEX_DIR="$BUILD_DIR/hex"

# 创建目录
mkdir -p "$OBJ_DIR" "$HEX_DIR"

echo -e "${YELLOW}STC89C52RC 模板项目构建脚本${NC}"
echo "=================================="

# 编译库文件
echo -e "${YELLOW}编译库文件...${NC}"
LIB_OBJECTS=""
for lib_file in "$LIB_DIR"/*.c; do
    if [ -f "$lib_file" ]; then
        lib_name=$(basename "$lib_file" .c)
        obj_file="$OBJ_DIR/${lib_name}.rel"
        echo "编译 $lib_file -> $obj_file"
        if $CC $CFLAGS -c "$lib_file" -o "$obj_file"; then
            LIB_OBJECTS="$LIB_OBJECTS $obj_file"
            echo -e "${GREEN}✓ $lib_name 编译成功${NC}"
        else
            echo -e "${RED}✗ $lib_name 编译失败${NC}"
            exit 1
        fi
    fi
done

# 编译主程序
echo -e "\n${YELLOW}编译主程序...${NC}"
if $CC $CFLAGS "$SRC_DIR/main.c" $LIB_OBJECTS -o "$BUILD_DIR/main.ihx"; then
    echo -e "${GREEN}✓ 主程序编译成功${NC}"
    if packihx "$BUILD_DIR/main.ihx" > "$HEX_DIR/main.hex"; then
        echo -e "${GREEN}✓ main.hex 生成成功${NC}"
    fi
else
    echo -e "${RED}✗ 主程序编译失败${NC}"
fi

# 编译GPIO Demo
echo -e "\n${YELLOW}编译GPIO Demo...${NC}"
if $CC $CFLAGS -DRUN_GPIO_DEMO "$SRC_DIR/GPIO_DEMO.c" $LIB_OBJECTS -o "$BUILD_DIR/gpio_demo.ihx"; then
    echo -e "${GREEN}✓ GPIO Demo编译成功${NC}"
    if packihx "$BUILD_DIR/gpio_demo.ihx" > "$HEX_DIR/gpio_demo.hex"; then
        echo -e "${GREEN}✓ gpio_demo.hex 生成成功${NC}"
    fi
else
    echo -e "${RED}✗ GPIO Demo编译失败${NC}"
fi

# 编译UART Demo
echo -e "\n${YELLOW}编译UART Demo...${NC}"
if $CC $CFLAGS -DRUN_UART_DEMO "$SRC_DIR/UART_DEMO.c" $LIB_OBJECTS -o "$BUILD_DIR/uart_demo.ihx"; then
    echo -e "${GREEN}✓ UART Demo编译成功${NC}"
    if packihx "$BUILD_DIR/uart_demo.ihx" > "$HEX_DIR/uart_demo.hex"; then
        echo -e "${GREEN}✓ uart_demo.hex 生成成功${NC}"
    fi
else
    echo -e "${RED}✗ UART Demo编译失败${NC}"
fi

# 编译Timer Demo
echo -e "\n${YELLOW}编译Timer Demo...${NC}"
if $CC $CFLAGS -DRUN_TIMER_DEMO "$SRC_DIR/TIMER_DEMO.c" $LIB_OBJECTS -o "$BUILD_DIR/timer_demo.ihx"; then
    echo -e "${GREEN}✓ Timer Demo编译成功${NC}"
    if packihx "$BUILD_DIR/timer_demo.ihx" > "$HEX_DIR/timer_demo.hex"; then
        echo -e "${GREEN}✓ timer_demo.hex 生成成功${NC}"
    fi
else
    echo -e "${RED}✗ Timer Demo编译失败${NC}"
fi

echo -e "\n${GREEN}构建完成！${NC}"
echo "生成的文件："
ls -la "$HEX_DIR"/*.hex 2>/dev/null || echo "没有生成hex文件"

echo -e "\n使用方法："
echo "  ./build.sh          - 编译所有目标"
echo "  ./build.sh clean    - 清理编译文件"
echo "  ./build.sh help     - 显示帮助信息"

# 处理命令行参数
case "$1" in
    clean)
        echo -e "\n${YELLOW}清理编译文件...${NC}"
        rm -rf "$BUILD_DIR"
        echo -e "${GREEN}✓ 清理完成${NC}"
        ;;
    help)
        echo -e "\n${YELLOW}帮助信息：${NC}"
        echo "本脚本用于编译STC89C52RC模板项目的所有demo"
        echo "每个demo都会生成独立的hex文件，可以单独下载到单片机"
        echo ""
        echo "生成的文件说明："
        echo "  main.hex       - 主程序"
        echo "  gpio_demo.hex  - GPIO演示程序"
        echo "  uart_demo.hex  - 串口演示程序"
        echo "  timer_demo.hex - 定时器演示程序"
        ;;
esac
