# 编译时错误检查示例

本示例展示了 `DELAY_TIMER_SELECT` 的编译时错误检查功能。

## 正确的配置

```c
// config.h 中的正确配置
#define DELAY_TIMER_SELECT 0    // 使用Timer0 - 正确
#define DELAY_TIMER_SELECT 1    // 使用Timer1 - 正确
#define DELAY_TIMER_SELECT 2    // 使用Timer2 - 正确（仅8052支持）
```

## 错误的配置

### 1. 无效的定时器编号

```c
// config.h 中的错误配置
#define DELAY_TIMER_SELECT 3    // 无效值
```

**编译错误信息：**
```
error: Invalid DELAY_TIMER_SELECT value. Supported values: 0 (Timer0), 1 (Timer1), 2 (Timer2 for 8052 only).
```

### 2. 在非8052芯片上使用Timer2

```c
// config.h 中的配置
#define DELAY_TIMER_SELECT 2    // 在8051芯片上使用Timer2
```

**编译错误信息：**
```
error: DELAY_TIMER_SELECT=2 (Timer2) is not supported on this MCU. Timer2 is only available on 8052 series.
```

## 优势

1. **编译时检查**：错误在编译时就被发现，而不是运行时
2. **清晰的错误信息**：明确指出支持的值和错误原因
3. **芯片兼容性检查**：自动检查Timer2是否在当前芯片上可用
4. **代码优化**：编译器生成最优化的代码，无运行时开销

## 实现原理

使用C预处理器的条件编译指令：

```c
#if (DELAY_TIMER_SELECT == 0)
    // Timer0 代码
#elif (DELAY_TIMER_SELECT == 1)
    // Timer1 代码
#elif (DELAY_TIMER_SELECT == 2) && defined(T2CON)
    // Timer2 代码（仅8052）
#elif (DELAY_TIMER_SELECT == 2) && !defined(T2CON)
    #error "Timer2 is not supported on this MCU"
#else
    #error "Invalid DELAY_TIMER_SELECT value"
#endif
```

这种方法确保了：
- 只有有效的定时器选择才能通过编译
- 生成的代码只包含实际使用的定时器代码
- 提供清晰的错误信息帮助开发者快速定位问题
