/**
 * @file delay_timer_select_example.c
 * @brief 延时定时器选择示例
 * <AUTHOR> Name
 * @date 2024
 */

#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include "config.h"
#include "delay.h"
#include "gpio.h"

/**
 * @brief 延时定时器选择示例
 */
void Delay_Timer_Select_Example(void)
{
    // 初始化LED引脚
    LED_Init();
    
    while(1)
    {
        // LED闪烁演示
        LED_Toggle();
        
        // 使用delay_ms_timer函数，定时器选择由DELAY_TIMER_SELECT宏定义决定
        // 在config.h中可以设置：
        // #define DELAY_TIMER_SELECT 0  // 使用Timer0
        // #define DELAY_TIMER_SELECT 1  // 使用Timer1  
        // #define DELAY_TIMER_SELECT 2  // 使用Timer2 (仅8052支持)
        delay_ms_timer(1000);
        
        LED_Toggle();
        
        // 也可以使用循环延时
        delay_ms_loop(1000);
    }
}

#ifdef RUN_DELAY_TIMER_SELECT_EXAMPLE
/**
 * @brief 主函数 - 仅在独立编译时使用
 */
void main(void)
{
    Delay_Timer_Select_Example();
}
#endif
