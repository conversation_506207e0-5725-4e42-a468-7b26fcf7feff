# STC89C52RC 开发模板

一个功能完整、兼容性强的STC89C52RC单片机开发模板，同时支持SDCC和Keil C51编译器。

## 核心特性

### 🔧 编译器兼容性
- **双编译器支持**: 完美兼容SDCC和Keil C51编译器
- **开发环境**: 支持VSCode + EIDE 和 Keil uVision 5
- **自动适配**: 根据编译器自动选择合适的头文件和语法

### ⚙️ 智能配置系统
- **动态时钟配置**: 所有时序计算基于FOSC自动调整
- **灵活波特率**: 支持任意波特率的自动计算
- **定时器选择**: 编译时选择延时使用的定时器
- **模块化配置**: 通过config.h统一管理所有配置

### 🚀 高效延时系统
- **双模式延时**: 支持循环延时和定时器延时
- **精确计算**: 根据系统时钟自动计算延时参数
- **编译时优化**: 使用宏条件编译，零运行时开销
- **错误检查**: 编译时验证定时器配置的有效性

### 📡 串口调试系统
- **printf重定向**: 支持标准printf函数输出
- **编译器适配**: 自动处理不同编译器的函数签名差异
- **调试开关**: 可通过宏定义启用/禁用调试功能
- **格式化输出**: 支持完整的printf格式化功能

### 🎯 多目标构建
- **独立编译**: 每个demo可单独编译为hex文件
- **一键构建**: 提供自动化构建脚本
- **批量生成**: 同时生成所有demo的hex文件

## 快速开始

### 📋 系统要求
- **编译器**: SDCC 4.0+ 或 Keil C51 v9.0+
- **开发环境**: VSCode + EIDE 或 Keil uVision 5
- **硬件**: STC89C52RC或兼容的8051单片机

### ⚡ 快速配置

**基本配置** (config.h):
```c
#define FOSC            12000000L   // 系统时钟频率
#define UART_BAUD       9600        // 串口波特率
#define DELAY_TIMER_SELECT 0        // 延时定时器选择
#define ENABLE_UART_DEBUG 1         // 启用printf调试
```

**支持的配置选项**:
- **时钟频率**: 11.0592MHz, 12MHz, 24MHz 或自定义
- **波特率**: 9600, 115200, 57600, 38400 或自定义
- **延时定时器**: Timer0(0), Timer1(1), Timer2(2)
- **调试模式**: 启用(1) 或 禁用(0)

## 开发环境

### VSCode + EIDE
1. 打开 `STC89C52RC-template.code-workspace`
2. 使用SDCC编译器
3. 编译输出在 `build/Debug/` 目录

### Keil uVision 5
1. 打开 `STC89C52RC-template.uvproj`
2. 使用Keil C51编译器
3. 编译输出在 `build/keil/` 目录

## 项目结构

```
├── inc/                    # 头文件目录
│   ├── config.h           # 系统配置文件
│   └── reg52.h            # 寄存器定义（兼容SDCC和Keil）
├── lib/                    # 库文件目录
│   ├── delay.c/h          # 延时函数库
│   ├── uart.c/h           # 串口函数库
│   ├── gpio.c/h           # GPIO函数库
│   └── timer.c/h          # 定时器函数库
├── src/                    # 源文件目录
│   ├── main.c             # 主程序
│   ├── GPIO_DEMO.c        # GPIO示例
│   ├── UART_DEMO.c        # 串口示例
│   └── TIMER_DEMO.c       # 定时器示例
├── build/                  # 编译输出目录
│   ├── Debug/             # EIDE/SDCC输出
│   └── keil/              # Keil输出
└── tools/                  # 工具目录
    └── stcflash.py        # STC下载工具
```

## 📚 使用指南

### 🔧 基本配置

**自定义时钟频率**:
```c
// 在main.c开头定义
#define FOSC 11059200L  // 11.0592MHz晶振
#include "config.h"
```

**自定义波特率**:
```c
// 在main.c开头定义
#define UART_BAUD 115200  // 高速波特率
#include "config.h"
```

### 💡 高级功能

**延时定时器选择**:
```c
// 在config.h中配置
#define DELAY_TIMER_SELECT 1  // 使用Timer1
```
- `0`: Timer0 (默认)
- `1`: Timer1
- `2`: Timer2 (仅8052支持)
- 编译时自动验证配置有效性

**printf调试输出**:
```c
#include <stdio.h>
#include "uart.h"

void main(void) {
    UART_Init();  // 初始化串口
    EA = 1;       // 开启全局中断

    printf("系统启动完成!\r\n");
    printf("时钟: %ld Hz\r\n", (long)FOSC);
}
```

## 编译说明

### 使用构建脚本（推荐）

#### SDCC环境
```bash
# 编译所有目标
./build.sh

# 清理编译文件
./build.sh clean

# 显示帮助
./build.sh help
```

#### Keil环境
```batch
REM 编译所有目标
build_keil.bat

REM 清理编译文件
build_keil.bat clean

REM 显示帮助
build_keil.bat help
```

### 手动编译

#### SDCC编译
```bash
# 在VSCode中按F7或使用命令
sdcc -mmcs51 -I./inc src/main.c lib/*.c -o build/Debug/main.ihx
```

#### Keil编译
在Keil uVision中按F7或使用菜单Project -> Build Target

### 编译输出

构建脚本会在 `build/hex/` 目录下生成以下文件：

- `main.hex` - 主程序
- `gpio_demo.hex` - GPIO演示程序
- `uart_demo.hex` - 串口演示程序
- `timer_demo.hex` - 定时器演示程序

## 下载说明

### 使用STC-ISP
1. 连接单片机到电脑
2. 打开STC-ISP软件
3. 选择对应的hex文件下载

### 使用stcflash.py
```bash
python tools/stcflash.py build/Debug/main.ihx
```

## ⚠️ 注意事项

### 配置要点
- **时钟匹配**: 确保FOSC设置与实际晶振频率一致
- **定时器冲突**: Timer1用于延时时，不能同时用于串口波特率发生器
- **调试开关**: 生产环境建议禁用ENABLE_UART_DEBUG以节省资源

### 兼容性说明
- **芯片支持**: STC89C52RC、STC89C51RC、AT89C52等8051兼容芯片
- **编译器**: 完全兼容SDCC和Keil C51，自动适配语法差异
- **开发环境**: 支持VSCode+EIDE和Keil uVision 5

### 性能优化
- **编译时优化**: 使用宏条件编译，运行时零开销
- **资源管理**: 可选功能模块，按需启用
- **代码精简**: 未使用的功能不会占用程序空间

---

**📖 更多示例和详细文档请参考 `examples/` 目录**
