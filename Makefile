# STC89C52RC 模板项目 Makefile
# 支持SDCC和Keil C51编译器

# 编译器设置
CC = sdcc
CFLAGS = -mmcs51 -I./inc -I./lib
LDFLAGS = -mmcs51

# 目录设置
SRC_DIR = src
LIB_DIR = lib
INC_DIR = inc
BUILD_DIR = build
OBJ_DIR = $(BUILD_DIR)/obj

# 源文件
LIB_SOURCES = $(wildcard $(LIB_DIR)/*.c)
LIB_OBJECTS = $(patsubst $(LIB_DIR)/%.c,$(OBJ_DIR)/%.rel,$(LIB_SOURCES))

# Demo目标
DEMOS = main gpio_demo uart_demo timer_demo

# 默认目标
all: $(DEMOS)

# 创建目录
$(OBJ_DIR):
	mkdir -p $(OBJ_DIR)

# 编译库文件
$(OBJ_DIR)/%.rel: $(LIB_DIR)/%.c | $(OBJ_DIR)
	$(CC) $(CFLAGS) -c $< -o $@

# 主程序目标
main: $(BUILD_DIR)/main.ihx

$(BUILD_DIR)/main.ihx: $(SRC_DIR)/main.c $(LIB_OBJECTS) | $(OBJ_DIR)
	$(CC) $(CFLAGS) $(SRC_DIR)/main.c $(LIB_OBJECTS) -o $@

# GPIO Demo目标
gpio_demo: $(BUILD_DIR)/gpio_demo.ihx

$(BUILD_DIR)/gpio_demo.ihx: $(SRC_DIR)/GPIO_DEMO.c $(LIB_OBJECTS) | $(OBJ_DIR)
	$(CC) $(CFLAGS) -DRUN_GPIO_DEMO $(SRC_DIR)/GPIO_DEMO.c $(LIB_OBJECTS) -o $@

# UART Demo目标
uart_demo: $(BUILD_DIR)/uart_demo.ihx

$(BUILD_DIR)/uart_demo.ihx: $(SRC_DIR)/UART_DEMO.c $(LIB_OBJECTS) | $(OBJ_DIR)
	$(CC) $(CFLAGS) -DRUN_UART_DEMO $(SRC_DIR)/UART_DEMO.c $(LIB_OBJECTS) -o $@

# Timer Demo目标
timer_demo: $(BUILD_DIR)/timer_demo.ihx

$(BUILD_DIR)/timer_demo.ihx: $(SRC_DIR)/TIMER_DEMO.c $(LIB_OBJECTS) | $(OBJ_DIR)
	$(CC) $(CFLAGS) -DRUN_TIMER_DEMO $(SRC_DIR)/TIMER_DEMO.c $(LIB_OBJECTS) -o $@

# 生成HEX文件
hex: $(DEMOS:%=$(BUILD_DIR)/%.hex)

$(BUILD_DIR)/%.hex: $(BUILD_DIR)/%.ihx
	packihx $< > $@

# 清理
clean:
	rm -rf $(BUILD_DIR)

# 显示帮助
help:
	@echo "可用目标:"
	@echo "  all        - 编译所有demo"
	@echo "  main       - 编译主程序"
	@echo "  gpio_demo  - 编译GPIO示例"
	@echo "  uart_demo  - 编译UART示例"
	@echo "  timer_demo - 编译Timer示例"
	@echo "  hex        - 生成所有HEX文件"
	@echo "  clean      - 清理编译文件"
	@echo "  help       - 显示此帮助信息"

.PHONY: all clean help hex $(DEMOS)
