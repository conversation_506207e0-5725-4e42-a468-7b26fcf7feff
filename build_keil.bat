@echo off
REM STC89C52RC 模板项目 Keil C51 构建脚本
REM 支持编译所有demo为独立的hex文件

setlocal enabledelayedexpansion

echo STC89C52RC 模板项目 Keil C51 构建脚本
echo ==========================================

REM 设置Keil路径（请根据实际安装路径修改）
set KEIL_PATH=C:\Keil_v5\UV4
set UV4=%KEIL_PATH%\UV4.exe

REM 检查Keil是否存在
if not exist "%UV4%" (
    echo 错误：找不到Keil uVision，请检查路径设置
    echo 当前设置路径：%KEIL_PATH%
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "build\keil" mkdir "build\keil"
if not exist "build\hex" mkdir "build\hex"

echo.
echo 开始编译各个目标...

REM 编译主程序
echo 编译主程序...
copy /y "src\main.c" "temp_main.c" >nul
"%UV4%" -b "STC89C52RC-template.uvproj" -t "Main" -o "build\keil\main_build.log"
if exist "build\keil\STC89C52RC-template.hex" (
    copy /y "build\keil\STC89C52RC-template.hex" "build\hex\main.hex" >nul
    echo ✓ 主程序编译成功
) else (
    echo ✗ 主程序编译失败
)

REM 编译GPIO Demo
echo 编译GPIO Demo...
REM 创建临时main文件，包含GPIO Demo
echo #define RUN_GPIO_DEMO > temp_main.c
echo #include "src/GPIO_DEMO.c" >> temp_main.c
copy /y "temp_main.c" "src\main.c" >nul
"%UV4%" -b "STC89C52RC-template.uvproj" -t "Main" -o "build\keil\gpio_demo_build.log"
if exist "build\keil\STC89C52RC-template.hex" (
    copy /y "build\keil\STC89C52RC-template.hex" "build\hex\gpio_demo.hex" >nul
    echo ✓ GPIO Demo编译成功
) else (
    echo ✗ GPIO Demo编译失败
)

REM 编译UART Demo
echo 编译UART Demo...
echo #define RUN_UART_DEMO > temp_main.c
echo #include "src/UART_DEMO.c" >> temp_main.c
copy /y "temp_main.c" "src\main.c" >nul
"%UV4%" -b "STC89C52RC-template.uvproj" -t "Main" -o "build\keil\uart_demo_build.log"
if exist "build\keil\STC89C52RC-template.hex" (
    copy /y "build\keil\STC89C52RC-template.hex" "build\hex\uart_demo.hex" >nul
    echo ✓ UART Demo编译成功
) else (
    echo ✗ UART Demo编译失败
)

REM 编译Timer Demo
echo 编译Timer Demo...
echo #define RUN_TIMER_DEMO > temp_main.c
echo #include "src/TIMER_DEMO.c" >> temp_main.c
copy /y "temp_main.c" "src\main.c" >nul
"%UV4%" -b "STC89C52RC-template.uvproj" -t "Main" -o "build\keil\timer_demo_build.log"
if exist "build\keil\STC89C52RC-template.hex" (
    copy /y "build\keil\STC89C52RC-template.hex" "build\hex\timer_demo.hex" >nul
    echo ✓ Timer Demo编译成功
) else (
    echo ✗ Timer Demo编译失败
)

REM 恢复原始main.c文件
if exist "temp_main.c" del "temp_main.c"
git checkout src\main.c 2>nul

echo.
echo 构建完成！
echo 生成的文件：
dir /b "build\hex\*.hex" 2>nul

echo.
echo 使用方法：
echo   build_keil.bat        - 编译所有目标
echo   build_keil.bat clean  - 清理编译文件
echo   build_keil.bat help   - 显示帮助信息

REM 处理命令行参数
if "%1"=="clean" (
    echo.
    echo 清理编译文件...
    if exist "build\keil" rmdir /s /q "build\keil"
    if exist "build\hex" rmdir /s /q "build\hex"
    echo ✓ 清理完成
    goto :end
)

if "%1"=="help" (
    echo.
    echo 帮助信息：
    echo 本脚本用于使用Keil C51编译STC89C52RC模板项目的所有demo
    echo 每个demo都会生成独立的hex文件，可以单独下载到单片机
    echo.
    echo 生成的文件说明：
    echo   main.hex       - 主程序
    echo   gpio_demo.hex  - GPIO演示程序
    echo   uart_demo.hex  - 串口演示程序
    echo   timer_demo.hex - 定时器演示程序
    echo.
    echo 注意：请确保Keil uVision已正确安装，并修改脚本中的KEIL_PATH变量
    goto :end
)

:end
pause
