/**
 * GPIO示例：LED闪烁
 *
 * 本示例演示如何使用GPIO库控制LED闪烁
 */

#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include "config.h"
#include "gpio.h"
#include "delay.h"


/* LED 配置 */
#ifndef LED_PIN
#ifdef __SDCC
#define LED_PIN         P0_0    // LED引脚
#else
#define LED_PIN         P0^0     // LED引脚
#endif
#endif

void GPIO_Demo(void)
{
    GPIO_SetHigh(LED_PIN);

    while(1)
    {
        // 翻转LED状态
        GPIO_Toggle(LED_PIN);
        // 延时500ms
        delay_ms(500);
    }
}

/**
 * @brief 主函数 - 仅在独立编译时使用
 */
void main(void)
{
    GPIO_Demo();
}