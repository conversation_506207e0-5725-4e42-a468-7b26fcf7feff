/**
 * @file main.c
 * @brief STC89C52RC 开发模板主程序
 * <AUTHOR> Name
 * @date 2024
 *
 * 这是一个功能完整的STC89C52RC开发模板，展示了各种功能的使用方法
 */

#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include "config.h"
#include "delay.h"
#include "uart.h"
#include "gpio.h"
#include "timer.h"
#include <stdio.h>

/**
 * @brief 主程序演示函数
 */
void main(void)
{
    unsigned int counter = 0;

    // 初始化LED引脚
#ifdef __SDCC
    P0_0 = 1;  // 初始状态为高电平（LED熄灭）
#else
    P00 = 1;   // 初始状态为高电平（LED熄灭）
#endif

    // 初始化串口
    UART_Init();

    // 开启全局中断
    EA = 1;

    // 发送启动信息
    printf("=== STC89C52RC 开发模板 ===\r\n");
    printf("编译器: ");

#ifdef __SDCC
    printf("SDCC\r\n");
#else
    printf("Keil C51\r\n");
#endif

    printf("系统时钟: %ld Hz\r\n", (long)FOSC);
    printf("串口波特率: %d bps\r\n", UART_BAUD);
    printf("延时定时器: Timer%d\r\n", DELAY_TIMER_SELECT);

#if ENABLE_UART_DEBUG
    printf("调试模式: 已启用\r\n");
#else
    printf("调试模式: 已禁用\r\n");
#endif

    printf("\r\n系统启动完成，开始运行...\r\n\r\n");

    // 主循环
    while(1)
    {
        // LED闪烁演示
#ifdef __SDCC
        P0_0 = !P0_0;
#else
        P00 = !P00;
#endif
        printf("LED状态切换 - 计数: %u\r\n", counter);

        // 使用定时器延时
        delay_ms_timer(1000);

#ifdef __SDCC
        P0_0 = !P0_0;
#else
        P00 = !P00;
#endif
        printf("LED状态切换 - 计数: %u\r\n", counter);

        // 使用循环延时
        delay_ms_loop(1000);

        counter++;

        // 每10次循环显示一次系统信息
        if(counter % 10 == 0) {
            printf("\r\n=== 系统状态 ===\r\n");
            printf("运行时间: %u 秒\r\n", counter * 2);
            printf("内存使用: 正常\r\n");
            printf("系统状态: 运行中\r\n\r\n");
        }

        // 检查串口接收
        if(RI) {
            unsigned char c = UART_ReceiveByte();
            printf("接收到字符: '%c' (0x%02X)\r\n", c, c);

            // 简单的命令处理
            switch(c) {
                case 'h':
                case 'H':
                    printf("\r\n=== 帮助信息 ===\r\n");
                    printf("h/H - 显示帮助\r\n");
                    printf("r/R - 重置计数器\r\n");
                    printf("s/S - 显示系统状态\r\n");
                    printf("其他 - 回显字符\r\n\r\n");
                    break;

                case 'r':
                case 'R':
                    counter = 0;
                    printf("计数器已重置\r\n\r\n");
                    break;

                case 's':
                case 'S':
                    printf("\r\n=== 系统状态 ===\r\n");
                    printf("计数器: %u\r\n", counter);
                    printf("系统时钟: %ld Hz\r\n", (long)FOSC);
                    printf("串口波特率: %d bps\r\n", UART_BAUD);
                    printf("延时定时器: Timer%d\r\n", DELAY_TIMER_SELECT);
                    printf("LED状态: 正常闪烁\r\n");
                    printf("\r\n");
                    break;

                default:
                    printf("未知命令，输入 'h' 查看帮助\r\n");
                    break;
            }
        }
    }
}
