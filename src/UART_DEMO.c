/**
 * 串口示例：发送和接收数据
 *
 * 本示例演示如何使用串口发送数据和接收数据
 */

#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include <stdio.h>

#define ENABLE_UART_DEBUG     1

#include "config.h"
#include "uart.h"
#include "delay.h"

void UART_Demo(void)
{
    unsigned int counter = 0;

    // 初始化串口
    UART_Init();

    // 开启全局中断
    EA = 1;

    // 使用printf发送欢迎信息
    printf("=== UART Demo Started ===\r\n");
    printf("编译器: ");

#ifdef __SDCC
    printf("SDCC\r\n");
#else
    printf("Keil C51\r\n");
#endif

    printf("系统时钟: %ld Hz\r\n", (long)FOSC);
    printf("串口波特率: %d bps\r\n", UART_BAUD);
    printf("请输入字符进行测试...\r\n\r\n");

    while(1)
    {
        // 检查是否有接收到数据
        if(RI) {
            unsigned char c = UART_ReceiveByte();
            // 使用printf回显接收到的字符
            printf("接收: '%c' (ASCII: %d, HEX: 0x%02X)\r\n", c, c, c);

            // 特殊字符处理
            if(c == '\r' || c == '\n') {
                printf("检测到回车/换行符\r\n");
            } else if(c >= '0' && c <= '9') {
                printf("这是数字字符，值为: %d\r\n", c - '0');
            } else if((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z')) {
                printf("这是字母字符\r\n");
            }
            printf("\r\n");
        }

        // 每隔5秒发送一条状态消息
        if(counter % 5000 == 0) {
            printf("系统运行中... 计数: %u\r\n", counter / 1000);
        }

        counter++;
        delay_ms_timer(1);  // 1ms延时
    }
}

/**
 * @brief 主函数 - 仅在独立编译时使用
 */
void main(void)
{
    UART_Demo();
}